<template>
  <VxeGrid
    class="bg-card ml-2 flex-1 rounded-lg border p-2"
    v-bind="detailListOptions"
    ref="detailListRef"
    @cell-click="handDetailListCellClick"
    @edit-closed="handleRightEditClose"
  >
    <!-- 税率类型  -->
    <template #typeEditor="{ row }">
      <vxe-select v-model="row.type" :options="TaxRateDict" />
    </template>
    <template #type="{ row }">
      {{ TaxRateDict.find((item) => item.value === row.type)?.label }}
    </template>
    <!--  执行时间  -->
    <template #executeDate="{ row }">
      {{
        row.executeDate && row.executeDate !== ''
          ? dayjs(row.executeDate).format('YYYY/MM/DD')
          : ''
      }}
    </template>
    <!--  税率   -->
    <template #taxRate="{ row }">
      <div class="flex justify-center">
        <div class="mr-1">{{ row.taxRate }}</div>
        <ElTooltip placement="top-start" :visible="row.changeLogVisible">
          <template #content>
            <div v-if="row.changeLogDetail">
              <div v-if="row.changeLogDetail.length > 0">
                <div class="flex" v-for="v in row.changeLogDetail" :key="v.id">
                  <div class="mr-1">
                    {{ dayjs(v.updateAt).format('YYYY年MM月DD日HH点mm分') }}
                  </div>
                  <div>{{ v.opreateUserName }} 修改税率</div>
                  <div>由 {{ v.oldValue }} 修改为 {{ v.newValue }}</div>
                </div>
              </div>
              <div v-else class="flex">暂无变更记录</div>
            </div>
            <div class="flex" v-else>
              <ElIcon>
                <Loading />
              </ElIcon>
            </div>
          </template>
          <div @click="getChangeLogData({ row })">
            <ElIcon>
              <InfoFilled />
            </ElIcon>
          </div>
        </ElTooltip>
      </div>
    </template>
  </VxeGrid>
</template>

<script setup lang="ts">
import { onMounted, reactive, ref, watch } from 'vue';

import { InfoFilled, Loading } from '@element-plus/icons-vue';
import dayjs from 'dayjs';
import { ElIcon, ElMessage, ElTooltip } from 'element-plus';

import {
  QueryTaxrateDictionaryChangeLog,
  UpdateTaxRateDicDetail,
} from '#/api/enterpriseCenter/enterpriseStandards/taxRateDictRelease';
import { SelectDetailsList } from '#/api/projectCenter/projectSetting/businessCost';

const props = withDefaults(
  defineProps<{
    categoryId: string;
    dictTypeId: string;
    quoteId: string;
    versionId: string;
  }>(),
  {
    dictTypeId: '',
    categoryId: '',
    versionId: '',
    quoteId: '',
  },
);

const emit = defineEmits<{
  (e: 'detailRefresh'): void;
}>();

const TaxRateDict = ref([
  {
    label: '增值税专用发票',
    value: 'EXCLUSIVE_USE',
  },
  {
    label: '增值税普通发票',
    value: 'GENERAL_USE',
  },
]);

// 字典明细
const detailListRef = ref();
const detailListOptions = reactive<any>({
  height: '100%',
  size: 'mini',
  border: true,
  keepSource: true,
  align: 'center',
  showOverflow: true,
  virtualYConfig: {
    enabled: true,
    gt: 0,
  },
  columnConfig: {
    resizable: true,
  },
  editConfig: {
    trigger: 'dblclick',
    mode: 'cell',
    showStatus: true,
    showIcon: false,
  },
  rowConfig: {
    isCurrent: true,
  },
  columns: [
    {
      width: 100,
      title: '编码',
      field: 'code',
    },
    {
      width: 120,
      field: 'name',
      title: '名称',
    },
    {
      field: 'type',
      title: '发票类型',
      editRender: {},
      slots: {
        edit: 'typeEditor',
        default: 'type',
      },
    },
    {
      field: 'taxRate',
      title: '税率(请用,隔开)',
      slots: {
        default: 'taxRate',
      },
      editRender: {
        name: 'VxeInput',
        props: {
          placeholder: '请输入税率',
        },
      },
    },
    {
      field: 'executeDate',
      title: '执行时间',
      slots: {
        default: 'executeDate',
      },
    },
    {
      width: 120,
      field: 'remark',
      title: '备注',
    },
  ],
  data: [],
  // 移除右键菜单配置
  // menuConfig: {
  //   body: {
  //     options: [],
  //   },
  // },
});
const currentDetail = ref();
function handDetailListCellClick({ row }: { row: any }) {
  currentDetail.value = row;
}

async function handleRightEditClose({ row }: any) {
  let res = null;
  res = await UpdateTaxRateDicDetail(row.id, {
    name: row.name,
    code: row.code,
    taxrateDictionaryVersionId: row.versionId,
    taxrateDictionaryCategoryId: row.categoryId,
    type: row.type,
    taxRate: row.taxRate,
  });
  if (res) ElMessage.success('操作成功！');
  emit('detailRefresh');
}

// 获取明细数据
async function loadDetailData() {
  if (props.dictTypeId && props.versionId && props.categoryId) {
    detailListOptions.data = await SelectDetailsList(
      props.dictTypeId,
      props.versionId,
      props.categoryId,
    );
  }
}
// 移除明细右键删除功能
// async function handleDetailRightContextMenuClick({ menu }: { menu: any }) {
//   // 删除功能已移除
// }

// 变更记录
async function getChangeLogData({ row }: any) {
  row.changeLogVisible = !row.changeLogVisible;
  if (!row.changeLogVisible) return;
  // 获取变更记录数据
  const id = row.id;
  row.changeLogDetail = null;
  const res = await QueryTaxrateDictionaryChangeLog(id);
  row.changeLogDetail = res;
}

// 添加手动刷新方法
const refreshData = () => loadDetailData();

// 初始加载 + 监听ID变化
onMounted(refreshData);
watch([() => props.categoryId, () => props.versionId], refreshData);

// 暴露方法给父组件
defineExpose({ refreshData });
</script>

<style scoped lang="scss">
:deep(.treeSelect-box) {
  .el-select__wrapper {
    height: 33px !important;
    .el-select__selection {
      height: 25px !important;
    }
  }
}
</style>
