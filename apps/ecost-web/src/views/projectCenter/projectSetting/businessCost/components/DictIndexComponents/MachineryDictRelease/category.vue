<template>
  <VxeGrid
    class="bg-card rounded-lg border p-2"
    style="width: 300px"
    v-bind="categoryListOptions"
    ref="categoryListRef"
    @cell-click="handCategoryListCellClick"
  >
    <template #typeSlot="{ row }">
      {{ materialDict.find((item) => item.value === row.type)?.label }}
    </template>
  </VxeGrid>
</template>

<script setup lang="ts">
import { nextTick, onMounted, reactive, ref, watch } from 'vue';

import { SelectVersionList } from '#/api/projectCenter/projectSetting/businessCost';

import { materialDict } from '../../../data';

const props = withDefaults(
  defineProps<{
    dictTypeId: string;
  }>(),
  {
    dictTypeId: '',
  },
);
const emit = defineEmits<{
  (e: 'categoryEvent', payload: Object): void;
  (e: 'categoryRefresh'): void;
}>();

const categoryListOptions = reactive({
  size: 'mini',
  border: true,
  keepSource: true,
  align: 'center',
  showOverflow: true,
  virtualYConfig: {
    enabled: true,
    gt: 0,
  },
  columnConfig: {
    resizable: true,
  },
  treeConfig: {
    rowField: 'id',
    parentField: 'parentId',
    transform: true,
    expandAll: true,
  },
  rowConfig: {
    isCurrent: true,
  },
  columns: [
    {
      field: 'code',
      title: '编码',
      treeNode: true,
    },
    {
      field: 'name',
      title: '名称',
    },
    {
      field: 'remark',
      title: '备注',
    },
  ],
  data: [],
  // 移除右键菜单配置
  // menuConfig: {
  //   body: {
  //     options: [],
  //   },
  // },
});
const categoryListRef = ref();
const categoryCurrent = ref({});
async function handCategoryListCellClick({ row }: { row: any }) {
  categoryCurrent.value = row;
  emit('categoryEvent', {
    categoryId: row.id,
    versionId: row.versionId,
    quoteId: row.accountBusinessCostSubjectVersionId, // 引用id
  });
}
// 移除右键菜单处理函数
// async function handleCategoryRightContextMenuClick({ menu }) {
//   // 删除功能已移除
// }

// 获取分类数据
async function loadCategoryData() {
  if (!props.dictTypeId) return;

  categoryListOptions.data = await SelectVersionList(props.dictTypeId);
  await nextTick();
  categoryListRef.value?.setAllTreeExpand(true);
}

// 添加手动刷新方法
const refreshData = () => loadCategoryData();

// 初始加载 + 监听ID变化
onMounted(refreshData);
watch(() => props.dictTypeId, refreshData);

// 暴露方法给父组件
defineExpose({ refreshData });
</script>

<style scoped lang="scss"></style>
