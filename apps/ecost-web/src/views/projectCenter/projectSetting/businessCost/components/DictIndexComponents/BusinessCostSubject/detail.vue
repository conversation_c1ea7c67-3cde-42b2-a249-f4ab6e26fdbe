<template>
  <VxeGrid
    class="bg-card ml-2 flex-1 rounded-lg border p-2"
    v-bind="detailListOptions"
    ref="detailListRef"
    @cell-click="handDetailListCellClick"
  >
    <!-- 安全施工费 -->
    <template #isSafetyConstructionFee-slot="{ row }">
      <VxeCheckbox
        disabled
        v-model="row.isSafetyConstructionFee"
        :checked-value="true"
        :unchecked-value="false"
      />
    </template>
  </VxeGrid>
</template>

<script setup lang="ts">
import { onMounted, reactive, ref, watch } from 'vue';

import { DetailsList } from '#/api/projectCenter/projectSetting/businessCost';

const props = withDefaults(
  defineProps<{
    categoryId: string;
    dictTypeId: string;
    versionId: string;
  }>(),
  {
    dictTypeId: '',
    categoryId: '',
    versionId: '',
  },
);
// 字典明细
const detailListRef = ref();
const detailListOptions = reactive({
  size: 'mini',
  border: true,
  keepSource: true,
  align: 'center',
  showOverflow: true,
  virtualYConfig: {
    enabled: true,
    gt: 0,
  },
  columnConfig: {
    resizable: true,
  },
  rowConfig: {
    isCurrent: true,
  },
  columns: [
    {
      title: '编码',
      field: 'code',
    },
    {
      width: 150,
      field: 'name',
      title: '业务成本科目名称',
    },
    {
      field: 'unit',
      title: '单位',
    },
    {
      field: 'expenseCategory',
      title: '费用类别',
    },
    {
      field: 'accountingDescription',
      title: '核算说明',
    },
    {
      field: 'isSafetyConstructionFee',
      title: '安全施工费',
      slots: {
        default: 'isSafetyConstructionFee-slot',
      },
    },
    {
      field: 'financialCostSubjectName',
      title: '财务成本科目对照',
      width: 120,
    },
    {
      field: 'subjectMappingDescription',
      title: '科目对照说明',
    },
  ],
  data: [],
});
function handDetailListCellClick() {}
// 获取财务成本科目树
// const financialCostAccountTreeList = ref([]);
// async function getFinancialCostAccount() {
//   financialCostAccountTreeList.value = await TreeListFinancialCostAccount();
// }
// getFinancialCostAccount();

// 获取明细数据
async function loadDetailData() {
  if (props.versionId && props.categoryId) {
    detailListOptions.data = await DetailsList(
      props.dictTypeId,
      props.versionId,
      props.categoryId,
    );
  }
}

// 添加手动刷新方法
const refreshData = () => loadDetailData();

// 初始加载 + 监听ID变化
onMounted(refreshData);
watch([() => props.categoryId, () => props.versionId], refreshData);

// 暴露方法给父组件
defineExpose({ refreshData });
</script>

<style scoped lang="scss"></style>
