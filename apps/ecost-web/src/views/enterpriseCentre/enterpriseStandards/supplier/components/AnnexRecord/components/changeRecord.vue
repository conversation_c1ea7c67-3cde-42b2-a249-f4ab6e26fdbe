<template>
  <ElScrollbar class="pb-2 pl-2 pr-2 pt-2">
    <div v-if="props.recordsList.length > 0">
      <div
        class="change-record-box --el-box-shadow-light"
        v-for="item in props.recordsList"
        :key="item.id"
      >
        <div class="title">
          <span class="title-name">{{ item.changeBy }}</span>
          <span class="title-time">{{ item.createAt }}</span>
          <span class="title-change-num">
            有&nbsp;<span class="num">{{ item.changeNum }}</span
            >&nbsp;处修改
          </span>
        </div>
        <div
          class="change-filed-item"
          v-for="(filedItem, idx) in item.details"
          :key="filedItem.id"
        >
          <!--  普通字段  -->
          <div v-if="filedItem.fieldType === 'GENERAL'">
            <div class="filed filed-name">{{ filedItem.fieldName }}：</div>
            <div class="filed filed-change-before mb-2 mt-2">
              {{ filedItem.oldValue }}
            </div>
            <div class="filed filed-change-after">{{ filedItem.newValue }}</div>
          </div>
          <!--  附件类型  -->
          <div v-else-if="filedItem.fieldType === 'FILE'">
            <div
              v-if="
                !item.details.slice(0, idx).some((i) => i.fieldType === 'FILE')
              "
              class="filed filed-name"
            >
              附件：
            </div>
            <div
              class="filed mb-2 mt-2"
              :class="[
                filedItem.changeType === 'ADD'
                  ? 'filed-change-after'
                  : 'filed-change-before',
              ]"
            >
              {{ filedItem.newValue }}
            </div>
          </div>
          <!-- <ElDivider v-if="idx !== item.details.length - 1" /> -->
          <ElDivider v-if="filedItem.fieldType !== 'FILE'" />
        </div>
      </div>
    </div>
    <ElEmpty v-else description="暂无变更记录！" />
  </ElScrollbar>
</template>

<script setup lang="ts">
import { ElDivider, ElEmpty, ElScrollbar } from 'element-plus';

const props = defineProps({
  recordsList: {
    type: Array,
    default: () => [],
  },
});
</script>

<style scoped lang="scss">
.change-record-box {
  box-shadow: var(--el-box-shadow-light);
  padding: 15px;
  margin: 10px 5px 5px 5px;
  .title {
    display: flex;
    justify-content: space-evenly;
    align-items: center;
    font-size: 16px;
    font-weight: 600;

    .title-change-num {
      color: #ec943f;
      .num {
        color: red;
        font-weight: 500;
        font-size: 16px;
      }
    }
  }
  .change-filed-item {
    margin-top: 10px;

    .filed-name {
      font-size: 15px;
      font-weight: 500;
    }

    :deep(.el-divider) {
      margin-top: 15px;
      margin-bottom: 10px;
    }

    font-size: 14px;

    .filed {
      padding: 5px;
    }

    .filed-change-before {
      background-color: #faecec;
      white-space: nowrap;
      text-decoration: line-through;
      width: fit-content;
    }
    .filed-change-after {
      background-color: #ecf7ef;

      width: fit-content;
    }
  }
}
</style>
