<template>
  <div class="box-content">
    <ElDrawer
      modal-class="pointer-events-none"
      class="pointer-events-auto"
      v-model="dialogVisible"
      :size="sizeNum"
      @close="handleClose"
      :modal="false"
      v-loading="loading"
      destroy-on-close
    >
      <template #header>
        <div class="btn-box flex justify-end">
          <!--  内置数据无法更改 -->
          <ElButton
            v-if="!dataForm.isDefault"
            :disabled="
              !(actionPermissions.apCreate || actionPermissions.apUpdate) ||
              dataForm.isPublished
            "
            size="small"
            type="primary"
            @click="submit"
          >
            保存
          </ElButton>
          <!--  内置数据无法更改 -->
          <div v-if="!dataForm.isDefault">
            <ElButton
              v-if="!dataForm.isPublished"
              v-auth="actionPermissions.apUpdate"
              class="ml-2 mr-2"
              size="small"
              type="success"
              @click="publish"
            >
              发布
            </ElButton>
            <ElButton
              v-else
              v-auth="actionPermissions.apUpdate"
              class="ml-2 mr-2"
              size="small"
              type="success"
              @click="cancelPublished"
            >
              取消发布
            </ElButton>
          </div>
          <ElTooltip :content="isFullScreen ? '收起' : '全屏'" placement="top">
            <IconifyIcon
              @click="isFullScreen = !isFullScreen"
              class="icon-box mr-5"
              :icon="
                isFullScreen
                  ? 'majesticons:arrows-collapse-full'
                  : 'majesticons:arrows-expand-full-line'
              "
            />
          </ElTooltip>
        </div>
      </template>
      <ElForm
        :disabled="
          (dataForm.id
            ? !actionPermissions.apUpdate
            : !actionPermissions.apCreate) ||
          dataForm.isPublished ||
          dataForm.isDefault
        "
        :model="dataForm"
        size="small"
        :rules="formRules"
        ref="formRef"
        label-position="top"
        label-width="auto"
      >
        <BasicTitleBar title="基础信息" class="mb-3" />
        <ElRow :gutter="20">
          <ElCol :span="8">
            <ElFormItem label="供应商全称" prop="fullName">
              <ElInput
                v-model="dataForm.fullName"
                placeholder="请输入供应商全称"
              />
            </ElFormItem>
          </ElCol>
          <ElCol :span="8">
            <ElFormItem label="供应商简称" prop="simpleName">
              <ElInput
                v-model="dataForm.simpleName"
                placeholder="请输入供应商简称"
              />
            </ElFormItem>
          </ElCol>
          <ElCol :span="8">
            <ElFormItem label="统一社会信用代码" prop="creditCode">
              <ElInput
                v-model="dataForm.creditCode"
                placeholder="请输入统一社会信用代码"
              />
            </ElFormItem>
          </ElCol>
          <ElCol :span="8">
            <ElFormItem label="法定代表人/单位负责人">
              <ElInput
                v-model="dataForm.LegalBy"
                placeholder="请输入法定代表人/单位负责人"
              />
            </ElFormItem>
          </ElCol>
          <ElCol :span="8">
            <ElFormItem label="注册所在省/市/区">
              <ElCascader
                v-model="dataForm.regionCode"
                style="width: 100%"
                placeholder="请选择注册所在省/市/区"
                :options="RegioData"
                :props="{
                  value: 'id',
                  label: 'label',
                }"
                @change="handleCascaderChange"
              />
            </ElFormItem>
          </ElCol>
          <ElCol :span="8">
            <ElFormItem label="注册地址">
              <ElInput
                v-model="dataForm.registeredAddress"
                placeholder="请输入注册地址"
              />
            </ElFormItem>
          </ElCol>
          <ElCol :span="8">
            <ElFormItem label="主营业务">
              <ElInput
                v-model="dataForm.mainBusiness"
                placeholder="请输入主营业务"
              />
            </ElFormItem>
          </ElCol>
          <ElCol :span="8">
            <ElFormItem label="供应商分类" prop="classify">
              <ElSelect
                v-model="dataForm.classify"
                multiple
                placeholder="请选择供应商分类"
              >
                <ElOption
                  v-for="item in supplierCategory"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </ElSelect>
            </ElFormItem>
          </ElCol>
          <ElCol :span="8">
            <ElFormItem label="供应工作内容">
              <ElInput
                v-model="dataForm.jobContent"
                placeholder="请输入供应工作内容"
              />
            </ElFormItem>
          </ElCol>
          <ElCol :span="8">
            <ElFormItem label="供应区域">
              <ElInput v-model="dataForm.region" placeholder="请输入供应区域" />
            </ElFormItem>
          </ElCol>
          <ElCol :span="8">
            <ElFormItem label="单位类型">
              <ElInput
                v-model="dataForm.unitType"
                placeholder="请输入单位类型"
              />
            </ElFormItem>
          </ElCol>
          <ElCol :span="8">
            <ElFormItem label="纳税人资质" prop="taxpayerQualification">
              <ElSelect
                v-model="dataForm.taxpayerQualification"
                placeholder="请选择纳税人资质"
              >
                <ElOption
                  v-for="item in taxpayerType"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </ElSelect>
            </ElFormItem>
          </ElCol>
          <ElCol :span="8">
            <ElFormItem label="注册资金（万元）">
              <ElInput
                v-model="dataForm.registeredCapital"
                class="input-number"
                style="width: 100%"
                type="number"
                placeholder="请输入注册资金"
              />
            </ElFormItem>
          </ElCol>
          <ElCol :span="8">
            <ElFormItem label="成立时间">
              <ElDatePicker
                type="date"
                v-model="dataForm.establishAt"
                placeholder="请选择成立时间"
                value-format="YYYY-MM-DD"
              />
            </ElFormItem>
          </ElCol>
          <ElCol :span="8">
            <ElFormItem label="联系人">
              <ElInput
                v-model="dataForm.contactBy"
                placeholder="请输入联系人"
              />
            </ElFormItem>
          </ElCol>
          <ElCol :span="8">
            <ElFormItem label="联系电话">
              <ElInput
                v-model="dataForm.contactPhone"
                placeholder="请输入联系电话"
              />
            </ElFormItem>
          </ElCol>
          <ElCol :span="8">
            <ElFormItem label="联系电邮">
              <ElInput
                v-model="dataForm.contactEmail"
                placeholder="请输入联系电邮件"
              />
            </ElFormItem>
          </ElCol>
          <ElCol :span="8">
            <ElFormItem label="关联企业">
              <ElInput
                v-model="dataForm.relationEnterprise"
                placeholder="请输入关联企业"
              />
            </ElFormItem>
          </ElCol>
          <ElCol :span="8">
            <ElFormItem label="引进时间">
              <ElDatePicker
                type="date"
                v-model="dataForm.introductionAt"
                placeholder="请选择引进时间"
                value-format="YYYY-MM-DD"
              />
            </ElFormItem>
          </ElCol>
          <ElCol :span="8">
            <ElFormItem label="备注">
              <ElInput v-model="dataForm.remark" placeholder="请输入备注" />
            </ElFormItem>
          </ElCol>
        </ElRow>
      </ElForm>
      <div class="side-box">
        <div class="side-item annex" @click="handleClickSideItem('annex')">
          附件
        </div>
        <div class="side-item record" @click="handleClickSideItem('record')">
          变更记录
        </div>
      </div>
      <AnnexRecord
        :id="dataForm.id"
        :publish="dataForm.isPublished ? dataForm.isPublished : false"
        v-model:visible="nestingDrawer"
        v-model:code="sideShowStatus"
        v-model:files="fileList"
        @file-close="getFileList"
      />
    </ElDrawer>
  </div>
</template>

<script setup lang="ts">
import { computed, reactive, ref, watch } from 'vue';

import { IconifyIcon } from '@vben/icons';

import {
  ElButton,
  ElCascader,
  ElCol,
  ElDatePicker,
  ElDrawer,
  ElForm,
  ElFormItem,
  ElInput,
  ElMessage,
  ElOption,
  ElRow,
  ElSelect,
  ElTooltip,
} from 'element-plus';

import {
  AddSupplier,
  ByIdSupplier,
  CancelPublishSupplier,
  PublishSupplier,
  UpdateSupplier,
} from '#/api/enterpriseCenter/enterpriseStandards/supplier';
import BasicTitleBar from '#/components/BasicTitleBar/index.vue';
import { getNamesFromTreeByIds } from '#/utils/common';
import { getCurrentPremission } from '#/utils/permission';
import RegioData from '#/utils/RegioData';

import { supplierCategory, taxpayerType } from '../data';
import AnnexRecord from './AnnexRecord/AnnexRecord.vue';

const props = withDefaults(
  defineProps<{
    rowId: string;
    visible: boolean;
  }>(),
  {
    visible: false,
    rowId: '',
  },
);

const emit = defineEmits<{
  (e: 'update:visible', visible: boolean): void;
  (e: 'refresh'): void;
}>();

const { actionPermissions } = getCurrentPremission();

const loading = ref(false);
const formRef = ref();
const dataForm = ref<any>({
  accessoryList: [],
  regionCode: [],
});
const formRules = reactive({
  fullName: [
    {
      required: true,
      message: '请输入供应商全称',
      trigger: ['change', 'blur'],
    },
  ],
  simpleName: [
    {
      required: true,
      message: '请输入供应商简称',
      trigger: ['change', 'blur'],
    },
  ],
  creditCode: [
    {
      required: true,
      message: '请输入统一社会信用代码',
      trigger: ['change', 'blur'],
    },
  ],
  classify: [
    {
      required: true,
      message: '请选择供应商分类',
      trigger: ['change', 'blur'],
    },
  ],
  taxpayerQualification: [
    {
      required: true,
      message: '请选择纳税人资质',
      trigger: ['change', 'blur'],
    },
  ],
});

const fileList = ref([]);
async function getFileList(data: any) {
  fileList.value = data.map((item: any) => {
    const idx = item.name.lastIndexOf('.');
    const ext = idx === -1 ? '' : item.name.slice(idx + 1);
    return {
      fileName: item.name,
      fileExt: ext,
      fileKey: item.fileKey,
      fileSize: String(item.size),
      fileContentType: item.fileContentType,
    };
  });
}

function submit() {
  formRef.value.validate(async (valid: boolean) => {
    if (valid) {
      // 格式化文件数据
      // const fileList = processFileList(dataForm.value.accessoryList);
      // 是否存在删除的附件
      // const delFiles = findDeletedItems(supplerFilesList.value, fileList);
      // if (delFiles.length > 0) {
      //   // 已经删除的数据增加 type 标识
      //   delFiles.forEach((item: any) => (item.type = true));
      //   fileList = [...fileList, ...delFiles];
      // }
      let res = null;
      dataForm.value.registeredCapital = dataForm.value.registeredCapital
        ? Number(dataForm.value.registeredCapital)
        : null;
      if (dataForm.value.id) {
        const {
          supplierDirectoryAccessory,
          supplierDirectoryChangeRecord,
          ...reset
        } = dataForm.value;
        // 编辑
        res = await UpdateSupplier(dataForm.value.id, {
          ...reset,
          registeredProvince: dataForm.value.registeredProvince?.split('，')[0],
          registeredCity: dataForm.value.registeredProvince?.split('，')[1],
          registeredCounty: dataForm.value.registeredProvince?.split('，')[2],
          regionCode: dataForm.value.regionCode?.join(','),
          // accessoryList: fileList?.length > 0 ? fileList : [],
        });
      } else {
        // 新增
        res = await AddSupplier({
          ...dataForm.value,
          registeredProvince: dataForm.value.registeredProvince?.split('，')[0],
          registeredCity: dataForm.value.registeredProvince?.split('，')[1],
          registeredCounty: dataForm.value.registeredProvince?.split('，')[2],
          regionCode: dataForm.value.regionCode?.join(','),
          accessoryList: fileList.value?.length > 0 ? fileList.value : [],
        });
      }
      if (res) {
        ElMessage.success('操作成功！');
        await getFormData(res.id);
      }
    }
  });
}
// 发布
async function publish() {
  const res = await PublishSupplier(dataForm.value.id);
  if (res) {
    ElMessage.success('发布成功！');
    await getFormData(dataForm.value.id);
  }
}
// 取消发布
async function cancelPublished() {
  const res = await CancelPublishSupplier(dataForm.value.id);
  if (res) {
    ElMessage.success('操作成功！');
    await getFormData(dataForm.value.id);
  }
}

// 嵌套抽屉
const nestingDrawer = ref(false);
function handleClose() {
  supplerFilesList.value = [];
  emit('update:visible', false);
  emit('refresh');
}
// 是否全屏
const isFullScreen = ref(false);
const sizeNum = computed(() => {
  return isFullScreen.value ? '100%' : '70%';
});

const sideShowStatus = ref('annex');
function handleClickSideItem(code: 'annex' | 'record') {
  nestingDrawer.value = true;
  sideShowStatus.value = code;
}
// 供应商全量附件
const supplerFilesList = ref([]);
async function getFormData(id: string) {
  loading.value = true;
  const dataInfo = await ByIdSupplier(id).finally(
    () => (loading.value = false),
  );
  // 供应商附件
  // supplerFilesList.value = await FileAllSupplier(id);
  dataForm.value = {
    ...dataInfo,
    regionCode: dataInfo.regionCode?.split(','),
    // accessoryList: dataInfo.supplierDirectoryAccessory,
    // supplerFilesList.value?.length > 0 ? supplerFilesList.value : [],
  };
}
function handleCascaderChange(val) {
  const regisNames = getNamesFromTreeByIds(RegioData, val, 'label').split(',');
  dataForm.value.registeredProvince = regisNames[0];
  dataForm.value.registeredCity = regisNames[1];
  dataForm.value.registeredCounty = regisNames[2];
}

// 处理删除后的附件
// function findDeletedItems(fullList: any, modifiedList: any): any {
//   return fullList.filter((item: any) => {
//     const existsInModified = modifiedList.some((modifiedItem: any) => {
//       return modifiedItem.id === item.id;
//     });
//     return !existsInModified;
//   });
// }

watch(
  () => props.rowId,
  (newValue) => {
    if (newValue) {
      getFormData(newValue);
    } else {
      dataForm.value = {};
    }
  },
  {
    immediate: true,
  },
);

const dialogVisible = ref(props.visible);
watch(
  () => props.visible,
  (newValue) => {
    dialogVisible.value = newValue;
  },
);
</script>

<style scoped lang="scss">
.box-content {
  position: relative;

  :deep(.record-box) {
    :deep(.el-drawer) {
      .el-drawer__header {
        margin-bottom: 0;
      }
    }
  }

  .icon-box {
    font-size: 24px;
    outline: none;

    &:hover {
      color: #006be6;
      cursor: pointer;
    }
  }

  .input-number {
    :deep(.el-input__inner) {
      text-align: left !important;
    }
  }

  .side-box {
    position: absolute;
    top: 80px;
    right: 2px;
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 20px;

    .side-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      width: 100%;
      padding: 5px;
      font-size: 12px;
      color: #fff;
      text-align: center;

      &:hover {
        cursor: pointer;
      }
    }

    .annex {
      background-color: #5ac37d;
      border-top-left-radius: 5px;
      border-top-right-radius: 5px;
    }

    .record {
      background-color: #3f90f8;
      border-bottom-right-radius: 5px;
      border-bottom-left-radius: 5px;
    }
  }
}
</style>
