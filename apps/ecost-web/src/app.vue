<template>
  <ElConfigProvider :locale="elementLocale">
    <RouterView />
  </ElConfigProvider>
</template>

<script lang="ts" setup>
import { RouterView } from 'vue-router';

import { useElementPlusDesignTokens } from '@vben/hooks';

import { ElConfigProvider } from 'element-plus';

import { elementLocale } from '#/locales';

defineOptions({ name: 'App' });

useElementPlusDesignTokens();
</script>

<style lang="scss">
// 处理element-plus的 日期选择器宽度问题
.el-range-editor.el-input__wrapper {
  width: 100% !important;
}

.el-date-editor.el-input {
  width: 100% !important;
}
</style>
